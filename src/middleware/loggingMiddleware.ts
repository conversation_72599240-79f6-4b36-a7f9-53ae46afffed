import express from 'express';
import morgan from 'morgan';
import json from 'morgan-json';
import fs from 'fs';
import path from 'path';
import { v4 as uuidv4 } from 'uuid';

// Ensure logs directory exists
const logsDir = path.join(process.cwd(), 'logs');

if (!fs.existsSync(logsDir)) {
  fs.mkdirSync(logsDir, { recursive: true });
}

/**
 * Generate log file path for the current date
 * Format: DD-MMM-YY-api.log (e.g., 05-jun-25-api.log)
 */
function getLogFilePath(): string {
  const now = new Date();
  const day = String(now.getDate()).padStart(2, '0');
  const month = now.toLocaleDateString('en', { month: 'short' }).toLowerCase();
  const year = String(now.getFullYear()).slice(-2);
  const filename = `${day}-${month}-${year}-api.log`;
  return path.join(logsDir, filename);
}

/**
 * Get or create write stream for current day's log file
 */
function getLogFileStream(): fs.WriteStream {
  const logFilePath = getLogFilePath();
  return fs.createWriteStream(logFilePath, { flags: 'a' });
}

// Console logging middleware
export const consoleLoggingMiddleware = morgan('dev');

// Simple JSON format for basic morgan logging
const jsonFormat = json(':method :url :status :response-time ms');

// File logging middleware using morgan-json (basic format)
export const fileLoggingMiddleware = morgan(jsonFormat, {
  stream: {
    write: (message: string) => {
      // Create a new stream for each write to ensure daily rotation
      const stream = getLogFileStream();
      stream.write(message);
      stream.end();
    }
  }
});

// Enhanced request/response logging middleware for detailed logs
export const requestResponseLoggingMiddleware = (req: express.Request, res: express.Response, next: express.NextFunction) => {
  const startTime = Date.now();
  const logId = uuidv4();
  
  // Add logId to request for correlation
  (req as any).logId = logId;

  const originalSend = res.send;
  const originalJson = res.json;

  // Capture request data
  const requestData = {
    timestamp: new Date().toISOString(),
    method: req.method,
    url: req.url,
    headers: req.headers,
    body: req.body || null
  };

  let responseBody: any = null;
  let errorInfo: any = null;

  // Intercept response to capture response body
  res.send = function (body) {
    responseBody = body;
    if (res.statusCode >= 400) {
      // Try to extract error information from response body
      try {
        const parsedBody = typeof body === 'string' ? JSON.parse(body) : body;
        if (parsedBody && parsedBody.message) {
          errorInfo = {
            message: parsedBody.message,
            statusCode: res.statusCode,
            timestamp: new Date().toISOString()
          };
        } else {
          errorInfo = {
            message: `HTTP ${res.statusCode} Error`,
            statusCode: res.statusCode,
            timestamp: new Date().toISOString()
          };
        }
      } catch (e) {
        errorInfo = {
          message: `HTTP ${res.statusCode} Error`,
          statusCode: res.statusCode,
          timestamp: new Date().toISOString()
        };
      }
    }
    return originalSend.call(this, body);
  };

  res.json = function (body) {
    responseBody = body;
    if (res.statusCode >= 400) {
      errorInfo = {
        message: body?.message || `HTTP ${res.statusCode} Error`,
        statusCode: res.statusCode,
        timestamp: new Date().toISOString(),
        details: body
      };
    }
    return originalJson.call(this, body);
  };

  // Log after response is sent
  res.on('finish', () => {
    const responseTime = Date.now() - startTime;

    // Create a comprehensive JSON log entry
    const logEntry = {
      timestamp: new Date().toISOString(),
      id: logId,
      ip: req.ip,
      request: {
        timestamp: requestData.timestamp,
        method: requestData.method,
        url: requestData.url,
        headers: requestData.headers,
        body: requestData.body
      },
      response: {
        status: res.statusCode,
        headers: res.getHeaders(),
        body: responseBody,
        responseTime: `${responseTime}ms`
      },
      error: errorInfo || false
    };

    // Write JSON log entry (one per line for easy parsing)
    // Create a new stream for each write to ensure daily rotation
    const apiLogStream = getLogFileStream();
    apiLogStream.write(JSON.stringify(logEntry) + '\n');
    apiLogStream.end();
  });

  next();
};

// Global error handler middleware
export const errorLoggingMiddleware = (err: Error, req: express.Request, res: express.Response, next: express.NextFunction) => {
  const errorDetails = {
    message: err.message,
    name: err.name,
    stack: err.stack,
    statusCode: res.statusCode || 500,
    timestamp: new Date().toISOString(),
    url: req.url,
    method: req.method
  };

  // Create error log entry
  const errorLogEntry = {
    timestamp: new Date().toISOString(),
    id: uuidv4(),
    ip: req.ip,
    request: {
      timestamp: new Date().toISOString(),
      method: req.method,
      url: req.url,
      headers: req.headers,
      body: req.body || null
    },
    response: {
      status: res.statusCode || 500,
      headers: res.getHeaders(),
      body: null,
      responseTime: "N/A"
    },
    error: errorDetails
  };

  // Write error log entry with daily rotation
  const apiLogStream = getLogFileStream();
  apiLogStream.write(JSON.stringify(errorLogEntry) + '\n');
  apiLogStream.end();

  // Send error response if not already sent
  if (!res.headersSent) {
    res.status(500).json({
      success: false,
      message: 'Internal Server Error',
      data: null
    });
  }
};

// Export utility functions for other modules to use
export { getLogFilePath, getLogFileStream }; 