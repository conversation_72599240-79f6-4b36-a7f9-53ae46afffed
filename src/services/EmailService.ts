import { Resend } from 'resend';
import { IRawMaterialStockDetails } from '../features/raw_material_stock/models/IRawMaterialStock';
import { IEmailSendResult } from '../interfaces/EmailInterfaces';

export interface IEmailConfig {
    apiKey: string;
    fromEmail: string;
}

export class EmailService {
    private resend: Resend;
    private fromEmail: string;

    constructor(config: IEmailConfig) {
        this.resend = new Resend(config.apiKey);
        this.fromEmail = config.fromEmail;
    }

    async sendProblematicStocksReport(
        stocks: IRawMaterialStockDetails[],
        recipients: string[]
    ): Promise<IEmailSendResult> {
        try {
            if (stocks.length === 0) {
                console.log('No problematic stocks found. Email not sent.');
                return {
                    success: true,
                    messageId: 'skipped',
                    timestamp: new Date()
                };
            }

            const htmlContent = this.generateStockReportHTML(stocks);
            const subject = `🚨 Problematic Stock Alert - ${stocks.length} Issues Found - ${new Date().toDateString()}`;

            // Send individual emails to each recipient to comply with Resend testing mode
            const emailResults = await Promise.all(
                recipients.map(async (recipient) => {
                    const emailData = {
                        from: this.fromEmail,
                        to: [recipient],
                        subject: subject,
                        html: htmlContent,
                    };

                    const result = await this.resend.emails.send(emailData);
                    
                    if (result.error) {
                        console.error(`Error sending email to ${recipient}:`, result.error);
                        return {
                            recipient,
                            success: false,
                            error: result.error
                        };
                    }

                    console.log(`Email sent successfully to ${recipient}. Email ID: ${result.data?.id}`);
                    return {
                        recipient,
                        success: true,
                        messageId: result.data?.id || 'unknown'
                    };
                })
            );

            // Check if all emails were sent successfully
            const allSuccessful = emailResults.every(result => result.success);
            const successfulCount = emailResults.filter(result => result.success).length;

            if (allSuccessful) {
                console.log(`✅ Problematic stocks report sent successfully to all ${recipients.length} recipients`);
                return {
                    success: true,
                    messageId: emailResults.map(r => r.messageId).join(','),
                    timestamp: new Date()
                };
            } else {
                console.error(`⚠️ Email sent to ${successfulCount}/${recipients.length} recipients`);
                const failedRecipients = emailResults
                    .filter(result => !result.success)
                    .map(result => result.recipient);

                return {
                    success: false,
                    error: {
                        code: 'PARTIAL_DELIVERY_FAILURE',
                        message: `Failed to send emails to: ${failedRecipients.join(', ')}`,
                        statusCode: 207
                    },
                    timestamp: new Date()
                };
            }

        } catch (error) {
            console.error('Error sending problematic stocks report:', error);
            return {
                success: false,
                error: {
                    code: 'EMAIL_SERVICE_ERROR',
                    message: error instanceof Error ? error.message : 'Unknown error occurred',
                    statusCode: 500
                },
                timestamp: new Date()
            };
        }
    }

    private generateStockReportHTML(stocks: IRawMaterialStockDetails[]): string {
        const currentDateTime = new Date().toLocaleString();
        
        const stockRows = stocks.map((stock, index) => {
            const issues = this.identifyStockIssues(stock);
            const issuesBadges = issues.map(issue => 
                `<span style="background-color: #dc3545; color: white; padding: 2px 6px; border-radius: 3px; font-size: 11px; margin: 1px;">${issue}</span>`
            ).join(' ');

            return `
                <tr style="border-bottom: 1px solid #dee2e6;">
                    <td style="padding: 12px; text-align: center;">${index + 1}</td>
                    <td style="padding: 12px;">${stock.rawMaterialName}</td>
                    <td style="padding: 12px;">${stock.sku || 'N/A'}</td>
                    <td style="padding: 12px;">${stock.categoryName}</td>
                    <td style="padding: 12px; text-align: right; ${stock.totalStock < 0 ? 'color: #dc3545; font-weight: bold;' : ''}">${stock.totalStock}</td>
                    <td style="padding: 12px; text-align: right; ${stock.usableStock < 0 ? 'color: #dc3545; font-weight: bold;' : ''}">${stock.usableStock}</td>
                    <td style="padding: 12px; text-align: right;">${stock.assignedStock}</td>
                    <td style="padding: 12px;">${issuesBadges}</td>
                </tr>
            `;
        }).join('');

        return `
        <!DOCTYPE html>
        <html lang="en">
        <head>
            <meta charset="UTF-8">
            <meta name="viewport" content="width=device-width, initial-scale=1.0">
            <title>Problematic Stock Report</title>
        </head>
        <body style="font-family: Arial, sans-serif; line-height: 1.6; color: #333; max-width: 1200px; margin: 0 auto; padding: 20px;">
            <div style="background: linear-gradient(135deg, #dc3545, #c82333); color: white; padding: 20px; border-radius: 8px; margin-bottom: 20px;">
                <h1 style="margin: 0; font-size: 24px;">🚨 Problematic Stock Alert</h1>
                <p style="margin: 5px 0 0 0; opacity: 0.9;">USI Inventory Management System</p>
            </div>

            <div style="background-color: #f8f9fa; padding: 15px; border-radius: 5px; margin-bottom: 20px;">
                <h2 style="color: #dc3545; margin-top: 0;">Summary</h2>
                <p><strong>Report Generated:</strong> ${currentDateTime}</p>
                <p><strong>Total Problematic Stocks:</strong> ${stocks.length}</p>
                <p><strong>Action Required:</strong> Immediate attention needed for inventory discrepancies</p>
            </div>

            <div style="background-color: white; border: 1px solid #dee2e6; border-radius: 5px; overflow: hidden;">
                <table style="width: 100%; border-collapse: collapse;">
                    <thead style="background-color: #343a40; color: white;">
                        <tr>
                            <th style="padding: 12px; text-align: center; font-weight: bold;">#</th>
                            <th style="padding: 12px; text-align: left; font-weight: bold;">Raw Material</th>
                            <th style="padding: 12px; text-align: left; font-weight: bold;">SKU</th>
                            <th style="padding: 12px; text-align: left; font-weight: bold;">Category</th>
                            <th style="padding: 12px; text-align: right; font-weight: bold;">Total Stock</th>
                            <th style="padding: 12px; text-align: right; font-weight: bold;">Usable Stock</th>
                            <th style="padding: 12px; text-align: right; font-weight: bold;">Assigned Stock</th>
                            <th style="padding: 12px; text-align: left; font-weight: bold;">Issues</th>
                        </tr>
                    </thead>
                    <tbody>
                        ${stockRows}
                    </tbody>
                </table>
            </div>

            <div style="background-color: #fff3cd; border: 1px solid #ffeaa7; padding: 15px; border-radius: 5px; margin-top: 20px;">
                <h3 style="color: #856404; margin-top: 0;">⚠️ Issue Types Explained:</h3>
                <ul style="margin-bottom: 0;">
                    <li><strong>Negative Total Stock:</strong> Total stock quantity is below zero</li>
                    <li><strong>Negative Usable Stock:</strong> Usable stock quantity is below zero</li>
                    <li><strong>Invalid Stock Ratio:</strong> Usable stock exceeds total stock (impossible scenario)</li>
                </ul>
            </div>

            <div style="background-color: #d1ecf1; border: 1px solid #bee5eb; padding: 15px; border-radius: 5px; margin-top: 20px;">
                <h3 style="color: #0c5460; margin-top: 0;">📋 Recommended Actions:</h3>
                <ol style="margin-bottom: 0;">
                    <li>Review and reconcile the identified stock discrepancies</li>
                    <li>Check recent stock transactions for data entry errors</li>
                    <li>Verify physical stock counts against system records</li>
                    <li>Update stock records to reflect accurate quantities</li>
                    <li>Investigate root cause to prevent future occurrences</li>
                </ol>
            </div>

            <div style="text-align: center; margin-top: 30px; padding-top: 20px; border-top: 1px solid #dee2e6; color: #6c757d; font-size: 12px;">
                <p>This is an automated report from USI Inventory Management System</p>
                <p>Generated on ${currentDateTime}</p>
            </div>
        </body>
        </html>
        `;
    }

    private identifyStockIssues(stock: IRawMaterialStockDetails): string[] {
        const issues: string[] = [];

        if (stock.totalStock < 0) {
            issues.push('Negative Total Stock');
        }

        if (stock.usableStock < 0) {
            issues.push('Negative Usable Stock');
        }

        if (stock.usableStock > stock.totalStock) {
            issues.push('Invalid Stock Ratio');
        }

        return issues;
    }

    async testConnection(): Promise<boolean> {
        try {
            // Test Resend connection by attempting to send a test request
            // Resend doesn't have a verify method, so we'll just check if we can make a basic API call
            const result = await this.resend.domains.list();
            
            if (result.error) {
                console.error('Resend API connection failed:', result.error);
                return false;
            }
            
            console.log('Resend API connection verified successfully');
            return true;
        } catch (error) {
            console.error('Resend API connection failed:', error);
            return false;
        }
    }
} 