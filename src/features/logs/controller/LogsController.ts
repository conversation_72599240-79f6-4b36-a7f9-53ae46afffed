import { NextFunction, Request, Response } from "express";
import { HelperMethods } from "../../../core/HelperMethods";
import { RepoProvider } from "../../../core/RepoProvider";
import { get } from "lodash";
import { MorganLogParser, IMorganLog } from "../utils/MorganLogParser";
import { MorganLogParser as UtilsMorganLogParser } from "../../../utils/MorganLogParser";
import path from 'path';
import fs from 'fs';
import { LogsValidations } from "../validations/LogsValidations";
import { ILogStatisticsResponse } from "../models/ILogStatistics";

export class LogsController {
    /**
     * Convert date to DD-MMM-YY format for log filename
     * Format: DD-MMM-YY (e.g., 05-jun-25)
     */
    private static formatDateForLogFile(date: string): string {
        const parsedDate = new Date(date + 'T00:00:00.000Z');
        const day = String(parsedDate.getUTCDate()).padStart(2, '0');
        const month = parsedDate.toLocaleDateString('en', { month: 'short', timeZone: 'UTC' }).toLowerCase();
        const year = String(parsedDate.getUTCFullYear()).slice(-2);
        return `${day}-${month}-${year}`;
    }

    /**
     * Parse date from DD-MMM-YY format filename
     */
    private static parseDateFromLogFilename(filename: string): string | null {
        // Match pattern: DD-MMM-YY-api.log
        const match = filename.match(/^(\d{2})-([a-z]{3})-(\d{2})-api\.log$/);
        if (!match) return null;

        const [, day, monthStr, year] = match;
        const monthMap: { [key: string]: string } = {
            'jan': '01', 'feb': '02', 'mar': '03', 'apr': '04', 'may': '05', 'jun': '06',
            'jul': '07', 'aug': '08', 'sep': '09', 'oct': '10', 'nov': '11', 'dec': '12'
        };
        
        const month = monthMap[monthStr];
        if (!month) return null;

        const fullYear = `20${year}`;
        return `${fullYear}-${month}-${day}`;
    }

    static async getAll(req: Request, res: Response, next: NextFunction) {
        try {

            const page = Number(get(req.query, "page"));
            const pageSize = Number(get(req.query, "pageSize"));

            const result = await RepoProvider.logRepo.getAllLogs(page, pageSize);
            if (!result.success) {
                res.status(500).send(result);
                return;
            }
            res.status(200).send(result);
        } catch (error) {
            HelperMethods.handleError(error);
        }
    }



    /**
     * Get paginated logs by date with filtering
     */
    static async getLogsByDatePaginated(req: Request, res: Response, next: NextFunction) {
        try {
            const validatedData = LogsValidations.parsePaginatedLogsByDateQuery(req);
            const { date, page, pageSize, method, statusCode, hasError, url, ip } = validatedData;

            const logsDir = path.join(process.cwd(), 'logs');
            const logFiles = LogsController.findLogFilesForDate(logsDir, date);

            if (logFiles.length === 0) {
                res.status(404).json({
                    success: false,
                    message: `No log files found for date: ${date}`,
                    data: null
                });
                return;
            }

            // Parse and filter logs using streaming for large files
            let allLogs: IMorganLog[] = [];
            
            for (const logFile of logFiles) {
                try {
                    // Check file size to determine parsing method
                    const stats = fs.statSync(logFile);
                    const fileSizeInMB = stats.size / (1024 * 1024);
                    
                    let logs: IMorganLog[];
                    if (fileSizeInMB > 100) {
                        // Use streaming for large files
                        logs = await MorganLogParser.parseLogStream(logFile);
                        // Filter by date
                        logs = logs.filter(log => {
                            const logDate = new Date(log.timestamp).toISOString().split('T')[0];
                            return logDate === date;
                        });
                    } else {
                        // Use regular parsing for smaller files
                        logs = MorganLogParser.parseLog(logFile);
                        // Filter by date
                        logs = logs.filter(log => {
                            const logDate = new Date(log.timestamp).toISOString().split('T')[0];
                            return logDate === date;
                        });
                    }
                    
                    allLogs.push(...logs);
                } catch (error) {
                    console.warn(`Failed to parse log file ${logFile}:`, error);
                }
            }

            // Apply filters manually
            let filteredLogs = allLogs;
            
            if (method) {
                filteredLogs = filteredLogs.filter((log: IMorganLog) => log.request.method === method);
            }
            
            if (statusCode) {
                filteredLogs = filteredLogs.filter((log: IMorganLog) => log.response.status === statusCode);
            }
            
            if (hasError !== undefined) {
                filteredLogs = filteredLogs.filter((log: IMorganLog) => {
                    const logHasError = log.error !== false;
                    return hasError === logHasError;
                });
            }
            
            if (url) {
                filteredLogs = filteredLogs.filter((log: IMorganLog) => 
                    log.request.url.toLowerCase().includes(url.toLowerCase())
                );
            }

            // Apply IP filter if specified
            let finalLogs = filteredLogs;
            if (ip) {
                finalLogs = filteredLogs.filter((log: IMorganLog) => 
                    log.ip && log.ip.toLowerCase().includes(ip.toLowerCase())
                );
            }

            // Sort by timestamp (newest first)
            finalLogs.sort((a: IMorganLog, b: IMorganLog) => new Date(b.timestamp).getTime() - new Date(a.timestamp).getTime());

            // Calculate pagination
            const totalData = finalLogs.length;
            const totalPages = Math.ceil(totalData / pageSize);
            const startIndex = (page - 1) * pageSize;
            const endIndex = startIndex + pageSize;
            const paginatedLogs = finalLogs.slice(startIndex, endIndex);

            // Pagination metadata
            const pagination = {
                currentPage: page,
                totalPages,
                totalData,
                pageSize,
                hasNextPage: page < totalPages,
                hasPreviousPage: page > 1
            };

            // Applied filters
            const appliedFilters = {
                date,
                ...(method && { method }),
                ...(statusCode && { statusCode }),
                ...(hasError !== undefined && { hasError }),
                ...(url && { url }),
                ...(ip && { ip })
            };

            res.status(200).json({
                success: true,
                message: `Found ${totalData} log entries for ${date} (showing page ${page} of ${totalPages})`,
                data: {
                    logs: paginatedLogs,
                    pagination,
                    filters: appliedFilters
                }
            });

        } catch (error) {
            console.error('Error fetching paginated logs by date:', error);
            res.status(500).json({
                success: false,
                message: "Internal server error",
                data: null
            });
        }
    }

    /**
     * Get all logs by date (legacy endpoint)
     */
    static async getLogsByDate(req: Request, res: Response, next: NextFunction) {
        try {
            const date = get(req.query, "date") as string;
            
            if (!date) {
                res.status(400).json({
                    success: false,
                    message: "Date parameter is required (format: YYYY-MM-DD)",
                    data: null
                });
                return;
            }

            // Validate date format
            const dateRegex = /^\d{4}-\d{2}-\d{2}$/;
            if (!dateRegex.test(date)) {
                res.status(400).json({
                    success: false,
                    message: "Invalid date format. Use YYYY-MM-DD",
                    data: null
                });
                return;
            }

            const logsDir = path.join(process.cwd(), 'logs');
            const logFiles = LogsController.findLogFilesForDate(logsDir, date);

            if (logFiles.length === 0) {
                res.status(404).json({
                    success: false,
                    message: `No log files found for date: ${date}`,
                    data: null
                });
                return;
            }

            // Parse and filter logs using streaming for large files
            let allLogs: IMorganLog[] = [];
            
            for (const logFile of logFiles) {
                try {
                    // Check file size to determine parsing method
                    const stats = fs.statSync(logFile);
                    const fileSizeInMB = stats.size / (1024 * 1024);
                    
                    let logs: IMorganLog[];
                    if (fileSizeInMB > 100) {
                        // Use streaming for large files
                        logs = await MorganLogParser.parseLogStream(logFile);
                        // Filter by date
                        logs = logs.filter((log: IMorganLog) => {
                            const logDate = new Date(log.timestamp).toISOString().split('T')[0];
                            return logDate === date;
                        });
                    } else {
                        // Use regular parsing for smaller files
                        logs = MorganLogParser.parseLog(logFile);
                        // Filter by date
                        logs = logs.filter((log: IMorganLog) => {
                            const logDate = new Date(log.timestamp).toISOString().split('T')[0];
                            return logDate === date;
                        });
                    }
                    
                    allLogs.push(...logs);
                } catch (error) {
                    console.warn(`Failed to parse log file ${logFile}:`, error);
                }
            }

            // Sort by timestamp
            allLogs.sort((a, b) => new Date(a.timestamp).getTime() - new Date(b.timestamp).getTime());

            res.status(200).json({
                success: true,
                message: `Found ${allLogs.length} log entries for ${date}`,
                data: allLogs
            });

        } catch (error) {
            console.error('Error fetching logs by date:', error);
            res.status(500).json({
                success: false,
                message: "Internal server error",
                data: null
            });
        }
    }

    /**
     * Get log statistics for a specific date
     */
    static async getLogStatsByDate(req: Request, res: Response, next: NextFunction) {
        try {
            const date = get(req.query, "date") as string;
            
            if (!date) {
                res.status(400).json({
                    success: false,
                    message: "Date parameter is required (format: YYYY-MM-DD)",
                    data: null
                });
                return;
            }

            // Validate date format
            const dateRegex = /^\d{4}-\d{2}-\d{2}$/;
            if (!dateRegex.test(date)) {
                res.status(400).json({
                    success: false,
                    message: "Invalid date format. Use YYYY-MM-DD",
                    data: null
                });
                return;
            }

            const logsDir = path.join(process.cwd(), 'logs');
            const logFiles = LogsController.findLogFilesForDate(logsDir, date);

            if (logFiles.length === 0) {
                res.status(404).json({
                    success: false,
                    message: `No log files found for date: ${date}`,
                    data: null
                });
                return;
            }

            // Parse and filter logs using streaming for large files
            let allLogs: IMorganLog[] = [];
            
            for (const logFile of logFiles) {
                try {
                    // Check file size to determine parsing method
                    const stats = fs.statSync(logFile);
                    const fileSizeInMB = stats.size / (1024 * 1024);
                    
                    let logs: IMorganLog[];
                    if (fileSizeInMB > 100) {
                        // Use streaming for large files
                        logs = await MorganLogParser.parseLogStream(logFile);
                        // Filter by date
                        logs = logs.filter((log: IMorganLog) => {
                            const logDate = new Date(log.timestamp).toISOString().split('T')[0];
                            return logDate === date;
                        });
                    } else {
                        // Use regular parsing for smaller files
                        logs = MorganLogParser.parseLog(logFile);
                        // Filter by date
                        logs = logs.filter((log: IMorganLog) => {
                            const logDate = new Date(log.timestamp).toISOString().split('T')[0];
                            return logDate === date;
                        });
                    }
                    
                    allLogs.push(...logs);
                } catch (error) {
                    console.warn(`Failed to parse log file ${logFile}:`, error);
                }
            }

            // Sort logs by timestamp for proper time range calculation
            allLogs.sort((a, b) => new Date(a.timestamp).getTime() - new Date(b.timestamp).getTime());
            
            // Use the comprehensive statistics function from utils
            const comprehensiveStats = UtilsMorganLogParser.getLogStatistics(allLogs);
            
            // Create time range for the specific date
            const dateStart = new Date(date + 'T00:00:00.000Z');
            const dateEnd = new Date(date + 'T23:59:59.999Z');
            
            const statsResponse: ILogStatisticsResponse = {
                date: date,
                totalRequests: comprehensiveStats.totalRequests,
                requestsByMethod: comprehensiveStats.requestsByMethod,
                requestsByStatus: comprehensiveStats.requestsByStatus,
                errorCount: comprehensiveStats.errorCount,
                averageResponseTime: comprehensiveStats.averageResponseTime,
                slowestRequests: comprehensiveStats.slowestRequests,
                topIPs: comprehensiveStats.topIPs,
                errorDistribution: comprehensiveStats.errorDistribution,
                timeRange: {
                    start: allLogs.length > 0 ? allLogs[0].timestamp : dateStart.toISOString(),
                    end: allLogs.length > 0 ? allLogs[allLogs.length - 1].timestamp : dateEnd.toISOString()
                }
            };

            res.status(200).json({
                success: true,
                message: "Statistics retrieved successfully",
                data: statsResponse
            });

        } catch (error) {
            console.error('Error fetching log stats by date:', error);
            res.status(500).json({
                success: false,
                message: "Internal server error",
                data: null
            });
        }
    }

    /**
     * Get available log dates
     */
    static async getAvailableLogDates(req: Request, res: Response, next: NextFunction) {
        try {
            const logsDir = path.join(process.cwd(), 'logs');
            
            if (!fs.existsSync(logsDir)) {
                res.status(404).json({
                    success: false,
                    message: "Logs directory not found",
                    data: null
                });
                return;
            }

            const files = fs.readdirSync(logsDir);
            const logFiles = files.filter(file => file.endsWith('-api.log'));
            
            const availableDates = new Set<string>();

            // Check all log files with new naming convention
            for (const file of logFiles) {
                const parsedDate = LogsController.parseDateFromLogFilename(file);
                if (parsedDate) {
                    availableDates.add(parsedDate);
                } else {
                    // Fallback: parse file content to extract dates
                    try {
                        const filePath = path.join(logsDir, file);
                        const logs = await MorganLogParser.parseLogStream(filePath);
                        const dates = logs.map((log: IMorganLog) => {
                            return new Date(log.timestamp).toISOString().split('T')[0];
                        });
                        dates.forEach((date: string) => availableDates.add(date));
                    } catch (error) {
                        console.warn(`Failed to parse log file ${file}:`, error);
                    }
                }
            }

            const sortedDates = Array.from(availableDates).sort().reverse(); // Most recent first

            res.status(200).json({
                success: true,
                message: `Found ${sortedDates.length} days with log data`,
                data: sortedDates
            });

        } catch (error) {
            console.error('Error fetching available log dates:', error);
            res.status(500).json({
                success: false,
                message: "Internal server error",
                data: null
            });
        }
    }

    /**
     * Helper method to find log files for a specific date
     */
    private static findLogFilesForDate(logsDir: string, date: string): string[] {
        const logFiles: string[] = [];
        
        try {
            const files = fs.readdirSync(logsDir);
            const formattedDate = LogsController.formatDateForLogFile(date);
            const targetFilename = `${formattedDate}-api.log`;
            
            // Look for the specific date's log file
            const targetFilePath = path.join(logsDir, targetFilename);
            if (fs.existsSync(targetFilePath)) {
                logFiles.push(targetFilePath);
            }

            // Also check current day's file if it matches the requested date
            const today = new Date().toISOString().split('T')[0];
            if (date === today) {
                const todayFormatted = LogsController.formatDateForLogFile(today);
                const todayFilename = `${todayFormatted}-api.log`;
                const todayFilePath = path.join(logsDir, todayFilename);
                if (fs.existsSync(todayFilePath) && !logFiles.includes(todayFilePath)) {
                    logFiles.push(todayFilePath);
                }
            }

        } catch (error) {
            console.warn('Error finding log files:', error);
        }

        return logFiles;
    }
}