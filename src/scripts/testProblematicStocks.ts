import { ProblematicStocksCronJob } from '../jobs/ProblematicStocksCronJob';
import { sequelizeInit, WorkingENV } from '../sequelize_init';
import { SequelizeAssociations } from '../sequelize_associations';
import { RepoProvider } from '../core/RepoProvider';

/**
 * Test script for Problematic Stocks Cron Job
 * 
 * Usage:
 * 1. Set your environment variables (RESEND_API_KEY, EMAIL_FROM)
 * 2. Run: npx ts-node src/scripts/testProblematicStocks.ts
 */

async function testProblematicStocks() {
    console.log('🧪 Testing Problematic Stocks Email System');
    console.log('=========================================\n');

    try {
        // Initialize database connection
        console.log('📡 Connecting to database...');
        await sequelizeInit.authenticate();
        console.log(`✅ Database connected successfully in ${WorkingENV} mode`);

        // Connect to Redis
        console.log('🔗 Connecting to Redis...');
        await RepoProvider.redisServerRepository.connect();
        console.log('✅ Redis connected successfully');

        // Setup database associations
        await SequelizeAssociations.associate();
        await sequelizeInit.sync();
        console.log('✅ Database associations loaded\n');

        // Create cron job instance
        console.log('🛠️  Creating cron job instance...');
        const testRecipients = ['<EMAIL>']; // Test with verified email
        const cronJob = new ProblematicStocksCronJob({
            emailRecipients: testRecipients,
            cronPattern: '0 9 * * *',
            timezone: 'Asia/Kolkata',
            enabled: true
        });

        // Test email connection
        console.log('📧 Testing email service connection...');
        const emailConnected = await cronJob.testEmailConnection();
        
        if (!emailConnected) {
            console.error('❌ Email service connection failed!');
            console.error('💡 Please check your RESEND_API_KEY and EMAIL_FROM environment variables');
            process.exit(1);
        }
        console.log('✅ Email service connection successful\n');

        // Run the problematic stocks check
        console.log('🔍 Running problematic stocks check...');
        await cronJob.runNow();

        console.log('\n✅ Test completed successfully!');
        console.log('📊 Check console output above for detailed results');
        console.log('📧 If problematic stocks were found, an email should have been sent');

    } catch (error) {
        console.error('💥 Test failed:', error);
        process.exit(1);
    } finally {
        // Close database connection
        await sequelizeInit.close();
        console.log('🔌 Database connection closed');
        process.exit(0);
    }
}

// Check if required environment variables are set
function checkEnvironmentVariables() {
    const required = ['RESEND_API_KEY', 'EMAIL_FROM'];
    const missing = required.filter(key => !process.env[key]);

    if (missing.length > 0) {
        console.error('❌ Missing required environment variables:');
        missing.forEach(key => console.error(`   - ${key}`));
        console.error('\n💡 Please set these variables in your .env file:');
        console.error('   RESEND_API_KEY=re_your_api_key_here');
        console.error('   EMAIL_FROM=<EMAIL>');
        process.exit(1);
    }
}

// Run the test
if (require.main === module) {
    console.log('🔧 Checking environment variables...');
    checkEnvironmentVariables();
    console.log('✅ Environment variables found\n');
    
    testProblematicStocks()
        .catch(error => {
            console.error('💥 Unhandled error:', error);
            process.exit(1);
        });
} 